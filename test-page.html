<!DOCTYPE html>
<html lang="en" manifest="https://example.com/manifest.appcache">
<head profile="https://example.com/profile">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Enhanced Link Extraction</title>

    <!-- Link elements -->
    <link rel="canonical" href="https://example.com/canonical">
    <link rel="stylesheet" href="https://example.com/styles.css">
    <link rel="icon" href="https://example.com/favicon.ico">
    <link rel="alternate" href="https://example.com/rss.xml" type="application/rss+xml">
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://www.googletagmanager.com/">

    <!-- Meta elements -->
    <meta http-equiv="refresh" content="5;url=https://example.com/redirect">
    <meta property="og:url" content="https://example.com/og-url">
    <meta property="og:image" content="https://example.com/og-image.jpg">
    <meta name="twitter:url" content="https://example.com/twitter-url">
    <meta name="twitter:image" content="https://example.com/twitter-image.jpg">

    <!-- Script elements -->
    <script src="https://example.com/script.js"></script>
</head>
<body background="https://example.com/background.jpg">
    <h1>Test Page for Enhanced Link Extraction</h1>

    <!-- Regular anchor links -->
    <p>Here are some <a href="https://example.com/page1">regular links</a> and
    <a href="/internal-page">internal links</a>.</p>

    <!-- Images with longdesc -->
    <img src="https://example.com/image1.jpg" alt="Test Image 1" longdesc="https://example.com/image1-desc.html">
    <img src="/local-image.png" alt="Local Image">

    <!-- Iframe with longdesc -->
    <iframe src="https://example.com/iframe-content" title="Test Iframe" longdesc="https://example.com/iframe-desc.html"></iframe>

    <!-- Citation elements -->
    <blockquote cite="https://example.com/quote-source">This is a quoted text.</blockquote>
    <p>This text has been <del cite="https://example.com/deletion-reason">deleted</del> and
    <ins cite="https://example.com/insertion-reason">inserted</ins>.</p>
    <p>As <q cite="https://example.com/quote-ref">someone once said</q>.</p>

    <!-- Form with various input types -->
    <form action="https://example.com/submit" method="post">
        <input type="text" name="test">
        <input type="image" src="https://example.com/submit-button.png" alt="Submit">
        <input type="submit" formaction="https://example.com/alt-submit" value="Alt Submit">
        <button type="submit" formaction="https://example.com/button-submit">Button Submit</button>
    </form>

    <!-- Media elements -->
    <audio src="https://example.com/audio.mp3" controls></audio>
    <video src="https://example.com/video.mp4" poster="https://example.com/video-poster.jpg" controls></video>

    <!-- Source elements -->
    <video controls>
        <source src="https://example.com/video.webm" type="video/webm">
        <source src="https://example.com/video.mp4" type="video/mp4">
    </video>

    <!-- Object with multiple attributes -->
    <object data="https://example.com/object.pdf"
            classid="https://example.com/object-class"
            codebase="https://example.com/object-codebase/"
            type="application/pdf"></object>

    <!-- Applet (deprecated but still checked) -->
    <applet codebase="https://example.com/applet-codebase/" code="MyApplet.class"></applet>

    <!-- Frame elements (deprecated but still checked) -->
    <frameset>
        <frame src="https://example.com/frame1.html" longdesc="https://example.com/frame1-desc.html">
        <frame src="https://example.com/frame2.html">
    </frameset>

    <!-- Command element (HTML5) -->
    <command icon="https://example.com/command-icon.png" label="Save">

    <!-- Image map -->
    <map name="testmap">
        <area shape="rect" coords="0,0,100,100" href="https://example.com/area1" alt="Area 1">
        <area shape="rect" coords="100,0,200,100" href="https://example.com/area2" alt="Area 2">
    </map>

    <!-- Embed -->
    <embed src="https://example.com/embed.swf" type="application/x-shockwave-flash">

    <!-- More script elements -->
    <script src="/local-script.js"></script>

    <!-- Broken links for testing -->
    <a href="https://httpbin.org/status/404">Broken Link (404)</a>
    <a href="https://httpbin.org/status/500">Server Error (500)</a>
    <a href="https://httpbin.org/status/200">Working Link (200)</a>
</body>
</html>
