# Broken Link Checker MVP

A modern web application that crawls websites to find broken links using Sc<PERSON>y for crawling and <PERSON><PERSON><PERSON> for link validation.

## Features

- **Web Crawling**: Uses Scrapy to recursively crawl websites and extract all internal and external links
- **Link Validation**: Uses <PERSON><PERSON><PERSON> to efficiently check links for broken status
- **Real-time Updates**: WebSocket connection provides live progress updates and results
- **Modern UI**: Clean, responsive interface built with React and Tailwind CSS
- **Progress Tracking**: Visual progress bar showing crawling and checking status
- **Live Results**: Broken links table updates in real-time as they are discovered

## Architecture

- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: FastAPI + Scrapy + Lychee
- **Real-time Communication**: WebSockets
- **Link Crawling**: Scrapy spider for recursive website crawling
- **Link Checking**: Lychee command-line tool for fast link validation

## Prerequisites

- Python 3.9+
- Node.js 18+
- Lychee (installed via Homebrew: `brew install lychee`)

## Installation

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create and activate a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Verify Lychee is installed:
   ```bash
   lychee --version
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

## Running the Application

### Start the Backend

1. Navigate to the backend directory and activate the virtual environment:
   ```bash
   cd backend
   source venv/bin/activate
   ```

2. Start the FastAPI server:
   ```bash
   python main.py
   ```

   The backend will be available at: http://localhost:8000

### Start the Frontend

1. In a new terminal, navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

   The frontend will be available at: http://localhost:5173

## Usage

1. Open your browser and go to http://localhost:5173
2. Enter a website URL in the input field (e.g., `https://example.com`)
3. Click "Check Links" to start the process
4. Watch the progress bar for real-time updates
5. View broken links as they are discovered in the table below

## API Endpoints

- `GET /` - API health check
- `POST /check-links` - Start link checking process
  ```json
  {
    "url": "https://example.com"
  }
  ```
- `GET /job/{job_id}` - Get job status
- `WebSocket /ws` - Real-time updates

## Project Structure

```
├── backend/
│   ├── main.py              # FastAPI application
│   ├── lychee_wrapper.py    # Lychee integration
│   ├── scrapy_runner.py     # Scrapy integration
│   ├── linkchecker/         # Scrapy project
│   └── requirements.txt     # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── App.jsx         # Main React component
│   │   └── main.jsx        # React entry point
│   ├── package.json        # Node.js dependencies
│   └── tailwind.config.js  # Tailwind configuration
└── README.md
```

## Development Notes

- The Scrapy spider is configured to crawl both internal and external links
- Lychee is used for efficient parallel link checking
- WebSocket connection provides real-time progress updates
- Error handling is implemented for network issues and invalid URLs
- The UI is responsive and works on mobile devices

## Future Enhancements

- Database storage for crawl history
- User authentication and saved projects
- Advanced filtering and reporting options
- Scheduled crawling
- Email notifications for broken links
- Export results to CSV/JSON

## License

MIT License
