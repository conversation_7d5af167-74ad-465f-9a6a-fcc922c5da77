#!/usr/bin/env python3
"""
Simple test script to verify the broken link checker is working
"""

import requests
import json
import time

def test_api():
    """Test the API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Broken Link Checker API...")
    
    # Test health check
    print("\n1. Testing health check...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"   ✅ Health check: {response.json()}")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
        return False
    
    # Test link checking
    print("\n2. Testing link checking...")
    try:
        test_url = "https://httpbin.org"
        response = requests.post(
            f"{base_url}/check-links",
            json={"url": test_url},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get("job_id")
            print(f"   ✅ Link check started: {result}")
            
            # Check job status
            print("\n3. Checking job status...")
            for i in range(10):  # Check for up to 10 seconds
                time.sleep(1)
                status_response = requests.get(f"{base_url}/job/{job_id}")
                if status_response.status_code == 200:
                    status = status_response.json()
                    print(f"   📊 Status: {status.get('status')} - {status.get('message')}")
                    
                    if status.get('status') in ['completed', 'error']:
                        if status.get('status') == 'completed':
                            broken_links = status.get('broken_links', [])
                            print(f"   ✅ Job completed! Found {len(broken_links)} broken links")
                            if broken_links:
                                print("   🔗 Broken links:")
                                for link in broken_links[:3]:  # Show first 3
                                    print(f"      - {link.get('url')} ({link.get('status')})")
                        else:
                            print(f"   ❌ Job failed: {status.get('message')}")
                        break
                else:
                    print(f"   ❌ Failed to get job status: {status_response.status_code}")
                    break
            else:
                print("   ⏰ Job is still running after 10 seconds")
                
        else:
            print(f"   ❌ Link check failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Link check failed: {e}")
        return False
    
    print("\n🎉 API test completed!")
    return True

def test_frontend():
    """Test that frontend is accessible"""
    print("\n🌐 Testing Frontend...")
    
    try:
        response = requests.get("http://localhost:5173/")
        if response.status_code == 200 and "<!doctype html>" in response.text.lower():
            print("   ✅ Frontend is accessible")
            return True
        else:
            print(f"   ❌ Frontend returned: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Frontend test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Broken Link Checker Tests")
    print("=" * 50)
    
    frontend_ok = test_frontend()
    api_ok = test_api()
    
    print("\n" + "=" * 50)
    if frontend_ok and api_ok:
        print("🎉 All tests passed! The application is working correctly.")
        print("\n📖 Usage:")
        print("   1. Open http://localhost:5173 in your browser")
        print("   2. Enter a website URL (e.g., https://example.com)")
        print("   3. Click 'Check Links' and watch the real-time progress")
        print("   4. View broken links in the table below")
    else:
        print("❌ Some tests failed. Please check the servers are running:")
        print("   - Frontend: npm run dev (in frontend directory)")
        print("   - Backend: python main.py (in backend directory)")
