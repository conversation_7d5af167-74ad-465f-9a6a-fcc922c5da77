from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
import json
from typing import List, Dict, Any, Optional
import subprocess
import tempfile
import os
from lychee_wrapper import <PERSON>ych<PERSON><PERSON>rapper
from scrapy_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import uuid
from urllib.parse import urlparse
import aiohttp
import re

app = FastAPI(title="Broken Link Checker API", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class LinkCheckRequest(BaseModel):
    url: str
    ignore_patterns: Optional[List[str]] = None

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()
lychee_wrapper = LycheeWrapper()
scrapy_runner = ScrapyRunner()

# Store active jobs and their processes
active_jobs: Dict[str, Dict[str, Any]] = {}
active_processes: Dict[str, asyncio.Task] = {}  # Track running tasks for abort functionality

# Default ignore patterns for common problematic URLs
DEFAULT_IGNORE_PATTERNS = [
    r'https://fonts\.googleapis\.com/?',  # Match with or without trailing slash
    r'https://www\.googletagmanager\.com/?',
    r'https://www\.google-analytics\.com/?',
    r'https://googleads\.g\.doubleclick\.net/?',
    r'https://connect\.facebook\.net/?',
    r'https://platform\.twitter\.com/?',
    r'https://www\.facebook\.com/tr/?',
    r'https://analytics\.google\.com/?',
    r'https://tagmanager\.google\.com/?',
    r'https://cdn\.jsdelivr\.net/?',
    r'https://cdnjs\.cloudflare\.com/?',
    r'https://unpkg\.com/?',
    r'mailto:',
    r'tel:',
    r'javascript:',
    r'data:',
]

@app.get("/")
async def root():
    return {"message": "Broken Link Checker API"}

@app.post("/check-links")
async def check_links(request: LinkCheckRequest, background_tasks: BackgroundTasks):
    """Start the link checking process"""
    # Normalize and validate URL
    try:
        # Normalize URL (add https:// if needed)
        normalized_url = normalize_url(request.url)

        # Validate the normalized URL
        parsed_url = urlparse(normalized_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return {"error": "Invalid URL format", "status": "error"}

        # Use the normalized URL for processing
        request.url = normalized_url

    except Exception as e:
        return {"error": f"Invalid URL: {str(e)}", "status": "error"}

    # Generate job ID
    job_id = str(uuid.uuid4())

    # Initialize job status
    active_jobs[job_id] = {
        "status": "started",
        "url": request.url,
        "progress": 0,
        "total_links": 0,
        "broken_links": [],
        "message": "Starting link check..."
    }

    # Start background task and track it for abort functionality
    task = asyncio.create_task(run_link_check(job_id, request.url, request.ignore_patterns))
    active_processes[job_id] = task

    # Add cleanup when task completes
    def cleanup_task(task):
        if job_id in active_processes:
            del active_processes[job_id]

    task.add_done_callback(lambda t: cleanup_task(t))

    return {
        "job_id": job_id,
        "message": f"Started checking links for {request.url}",
        "status": "started"
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Handle incoming WebSocket messages if needed
            await manager.send_personal_message(f"Message received: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/job/{job_id}")
async def get_job_status(job_id: str):
    """Get the status of a link checking job"""
    if job_id not in active_jobs:
        return {"error": "Job not found", "status": "error"}

    return active_jobs[job_id]

@app.post("/abort/{job_id}")
async def abort_job(job_id: str):
    """Abort a running link checking job"""
    if job_id not in active_jobs:
        return {"error": "Job not found", "status": "error"}

    if job_id not in active_processes:
        return {"error": "Job not running", "status": "error"}

    # Cancel the running task
    task = active_processes[job_id]
    task.cancel()

    # Update job status
    active_jobs[job_id]["status"] = "aborted"
    active_jobs[job_id]["message"] = "Job aborted by user"

    # Get current results (broken and healthy links checked so far)
    broken_links = active_jobs[job_id].get("broken_links", [])
    healthy_links = active_jobs[job_id].get("healthy_links", [])
    all_links = active_jobs[job_id].get("all_links", {"broken": [], "healthy": [], "excluded": []})

    # Try to get partial results from Lychee if available
    # Note: This is a best-effort approach since Lychee may not have partial results
    # In a real implementation, we might need to modify the Lychee wrapper to store partial results

    # Broadcast abort status with current results
    await manager.broadcast(json.dumps({
        "job_id": job_id,
        "status": "aborted",
        "message": "Job aborted by user",
        "broken_links": broken_links,
        "healthy_links": healthy_links,
        "all_links": all_links,
        "total_links": active_jobs[job_id].get("total_links", 0),
        "checked_links": active_jobs[job_id].get("checked_links", 0)
    }))

    # Clean up
    del active_processes[job_id]

    return {"message": "Job aborted successfully", "status": "aborted"}

def normalize_url(url: str) -> str:
    """
    Normalize URL by adding https:// if no protocol is specified

    Args:
        url: Input URL that may or may not have a protocol

    Returns:
        Normalized URL with protocol
    """
    url = url.strip()

    # If URL already has a protocol, return as is
    if re.match(r'^https?://', url, re.IGNORECASE):
        return url

    # If URL starts with //, add https:
    if url.startswith('//'):
        return f'https:{url}'

    # If URL looks like a domain (contains dots but no protocol), add https://
    if '.' in url and not url.startswith(('http://', 'https://', 'ftp://', 'file://')):
        return f'https://{url}'

    # For anything else, assume it needs https://
    return f'https://{url}' if not url.startswith('https://') else url

async def follow_redirects(url: str) -> str:
    """
    Follow redirects to get the final URL

    Args:
        url: Initial URL to check

    Returns:
        Final URL after following redirects
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, allow_redirects=True, timeout=aiohttp.ClientTimeout(total=10)) as response:
                final_url = str(response.url)
                return final_url
    except Exception as e:
        # If redirect following fails, return original URL
        print(f"Warning: Could not follow redirects for {url}: {e}")
        return url

async def run_link_check(job_id: str, url: str, ignore_patterns: Optional[List[str]] = None):
    """Run the complete link checking process"""
    try:
        # Follow redirects to get the final URL
        active_jobs[job_id]["status"] = "redirects"
        active_jobs[job_id]["message"] = "Following redirects..."
        active_jobs[job_id]["progress"] = 2
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "redirects",
            "progress": 2,
            "message": "Following redirects..."
        }))

        final_url = await follow_redirects(url)
        if final_url != url:
            active_jobs[job_id]["message"] = f"Redirected to {final_url}"
            await manager.broadcast(json.dumps({
                "job_id": job_id,
                "message": f"Redirected to {final_url}",
                "progress": 3
            }))

        # Update job status
        active_jobs[job_id]["status"] = "crawling"
        active_jobs[job_id]["message"] = "Crawling website for links..."
        active_jobs[job_id]["progress"] = 5
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "crawling",
            "progress": 5,
            "message": "Crawling website for links..."
        }))

        # Run Scrapy spider to collect links
        # Use provided ignore patterns or fall back to defaults
        patterns_to_use = ignore_patterns if ignore_patterns else DEFAULT_IGNORE_PATTERNS
        links_data = await scrapy_runner.crawl_links(final_url,
            progress_callback=lambda msg, step=None, total=None: update_progress_with_details(job_id, msg, 5, 35, step, total),
            ignore_patterns=patterns_to_use)

        # Create URL to link data mapping for preserving context
        url_to_link_data = {link['url']: link for link in links_data}

        # Extract just URLs for Lychee and apply ignore patterns again as a safety measure
        all_links_urls = [link['url'] for link in links_data]

        # Filter out ignored URLs before sending to Lychee (safety check)
        filtered_links_urls = []
        filtered_links_data = []
        for url in all_links_urls:
            should_ignore = False
            for pattern in patterns_to_use:
                if re.search(pattern, url, re.IGNORECASE):
                    should_ignore = True
                    break
            if not should_ignore:
                filtered_links_urls.append(url)
                filtered_links_data.append(url_to_link_data[url])

        links_urls = filtered_links_urls
        links_data = filtered_links_data  # Keep the filtered link data with context

        active_jobs[job_id]["total_links"] = len(links_urls)
        active_jobs[job_id]["progress"] = 40
        active_jobs[job_id]["message"] = f"Found {len(links_urls)} links. Starting link validation..."
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "checking",
            "progress": 40,
            "total_links": len(links_urls),
            "checked_links": 0,
            "message": f"Found {len(links_urls)} links. Starting link validation..."
        }))



        # Process all links at once with real-time progress updates
        # Create URL to link data mapping for preserving context
        url_to_link_data = {link['url']: link for link in links_data}

        # Progress callback that updates for each individual link
        async def individual_progress_callback(message: str, checked: Optional[int] = None, total: Optional[int] = None, current_url: Optional[str] = None):
            if checked is not None and total is not None:
                # Calculate progress from 40% to 95% during link checking
                link_progress = (checked / total) * 55  # 55% range for link checking
                progress = min(95, 40 + link_progress)

                # Include current URL being processed in the message
                if current_url:
                    display_message = f"Checking: {current_url} ({checked}/{total})"
                else:
                    display_message = f"Checking links... ({checked}/{total})"

                active_jobs[job_id]["progress"] = int(progress)
                active_jobs[job_id]["checked_links"] = checked
                active_jobs[job_id]["message"] = display_message

                await manager.broadcast(json.dumps({
                    "job_id": job_id,
                    "status": "checking",
                    "progress": int(progress),
                    "total_links": total,
                    "checked_links": checked,
                    "message": display_message,
                    "current_url": current_url
                }))

        # Process all links at once
        result = await lychee_wrapper.check_links(links_urls, progress_callback=individual_progress_callback)

        # Extract results
        broken_links = lychee_wrapper.extract_broken_links(result)
        healthy_links = lychee_wrapper.extract_healthy_links(result)
        all_links_data = lychee_wrapper.extract_all_links(result)

        # Reconstruct full link information with context
        def reconstruct_links_with_context(lychee_links, link_data_map):
            reconstructed = []
            for lychee_link in lychee_links:
                url = lychee_link.get('url', '')
                if url in link_data_map:
                    # Merge Lychee result with original link context
                    full_link = {
                        **link_data_map[url],  # Original context (anchor_text, source_page, etc.)
                        **lychee_link  # Lychee result (status, etc.)
                    }
                    reconstructed.append(full_link)
                else:
                    # Fallback if mapping is missing
                    reconstructed.append(lychee_link)
            return reconstructed

        # Reconstruct with full context
        broken_links = reconstruct_links_with_context(broken_links, url_to_link_data)
        healthy_links = reconstruct_links_with_context(healthy_links, url_to_link_data)
        all_links_data["broken"] = broken_links
        all_links_data["healthy"] = healthy_links

        # Store final results in job data
        active_jobs[job_id]["broken_links"] = broken_links
        active_jobs[job_id]["healthy_links"] = healthy_links
        active_jobs[job_id]["all_links"] = all_links_data

        # Enhance all link types with source page information
        def enhance_links_with_source_info(links_list, links_data, final_url):
            """Helper function to enhance links with source page information"""
            links_lookup = {link['url']: link for link in links_data}

            # Create additional lookups for URL variations to handle normalization differences
            normalized_lookup = {}
            for link in links_data:
                url = link['url']
                # Add variations: with/without trailing slash, with/without www, etc.
                variations = [
                    url,
                    url.rstrip('/'),
                    url + '/' if not url.endswith('/') else url[:-1],
                ]
                for variation in variations:
                    normalized_lookup[variation] = link

            for link_item in links_list:
                found_source = None
                link_url = link_item['url']

                # Try exact match first
                if link_url in links_lookup:
                    found_source = links_lookup[link_url]
                # Try normalized variations
                elif link_url in normalized_lookup:
                    found_source = normalized_lookup[link_url]
                # Try without trailing slash
                elif link_url.rstrip('/') in links_lookup:
                    found_source = links_lookup[link_url.rstrip('/')]
                # Try with trailing slash
                elif (link_url + '/') in links_lookup:
                    found_source = links_lookup[link_url + '/']

                if found_source:
                    link_item['source_page'] = found_source['source_page']
                    link_item['link_type'] = found_source['link_type']
                    link_item['anchor_text'] = found_source['anchor_text']
                else:
                    # Fallback: use the final_url as source
                    link_item['source_page'] = final_url
                    link_item['link_type'] = 'unknown'
                    link_item['anchor_text'] = 'No text'

                # Ensure source_page is never None or empty
                if not link_item.get('source_page'):
                    link_item['source_page'] = final_url

        # Enhance all link types with source information
        enhance_links_with_source_info(broken_links, links_data, final_url)
        enhance_links_with_source_info(healthy_links, links_data, final_url)
        enhance_links_with_source_info(all_links_data['excluded'], links_data, final_url)

        # Calculate counts excluding excluded links
        excluded_count = len(all_links_data.get('excluded', []))
        total_processed_links = len(links_urls) - excluded_count

        # Update final status
        active_jobs[job_id]["status"] = "completed"
        active_jobs[job_id]["progress"] = 100
        active_jobs[job_id]["broken_links"] = broken_links
        active_jobs[job_id]["healthy_links"] = healthy_links
        active_jobs[job_id]["all_links"] = all_links_data
        active_jobs[job_id]["checked_links"] = total_processed_links
        active_jobs[job_id]["message"] = f"Completed! Found {len(broken_links)} broken links out of {total_processed_links} total links."

        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "completed",
            "progress": 100,
            "total_links": total_processed_links,
            "checked_links": total_processed_links,
            "broken_links": broken_links,
            "healthy_links": healthy_links,
            "all_links": all_links_data,
            "message": f"Completed! Found {len(broken_links)} broken links out of {total_processed_links} total links."
        }))

    except asyncio.CancelledError:
        # Handle job abortion
        active_jobs[job_id]["status"] = "aborted"
        active_jobs[job_id]["message"] = "Job aborted by user"
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "aborted",
            "message": "Job aborted by user"
        }))
        raise  # Re-raise to properly handle the cancellation
    except Exception as e:
        active_jobs[job_id]["status"] = "error"
        active_jobs[job_id]["message"] = f"Error: {str(e)}"
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "status": "error",
            "message": f"Error: {str(e)}"
        }))

async def update_progress(job_id: str, message: str):
    """Update progress for a job"""
    if job_id in active_jobs:
        active_jobs[job_id]["message"] = message
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "message": message
        }))

async def update_progress_with_details(job_id: str, message: str, min_progress: int, max_progress: int, step: Optional[int] = None, total_steps: Optional[int] = None):
    """Update progress with more granular details"""
    if job_id in active_jobs:
        # Calculate progress within the given range
        if step is not None and total_steps is not None and total_steps > 0:
            # Calculate progress based on actual steps completed
            step_progress = (step / total_steps) * (max_progress - min_progress)
            current_progress = min(max_progress, min_progress + step_progress)
        else:
            # Fallback to incremental progress
            current_progress = active_jobs[job_id].get("progress", min_progress)
            if current_progress < max_progress:
                current_progress = min(max_progress, current_progress + 3)  # Smaller increments

        active_jobs[job_id]["message"] = message
        active_jobs[job_id]["progress"] = current_progress
        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "message": message,
            "progress": current_progress
        }))

async def update_lychee_progress(job_id: str, message: str, checked_links: Optional[int] = None, total_links: Optional[int] = None):
    """Update progress during Lychee checking with detailed statistics"""
    if job_id in active_jobs:
        # Calculate progress percentage
        if checked_links is not None and total_links is not None and total_links > 0:
            # Progress from 40% to 95% during link checking
            link_progress = (checked_links / total_links) * 55  # 55% range for link checking
            progress = min(95, 40 + link_progress)

            active_jobs[job_id]["checked_links"] = checked_links
        else:
            # Fallback progress calculation
            if "Starting" in message:
                progress = 40
                checked_links = 0
            elif "completed" in message.lower():
                progress = 95
                checked_links = active_jobs[job_id].get("total_links", 0)
            else:
                # More gradual progress increments
                current_progress = active_jobs[job_id].get("progress", 40)
                if current_progress < 95:
                    progress = min(95, current_progress + 2)  # Smaller increments
                else:
                    progress = current_progress
                checked_links = active_jobs[job_id].get("checked_links", 0)

        active_jobs[job_id]["message"] = message
        active_jobs[job_id]["progress"] = int(progress)
        if checked_links is not None:
            active_jobs[job_id]["checked_links"] = checked_links

        await manager.broadcast(json.dumps({
            "job_id": job_id,
            "message": message,
            "progress": int(progress),
            "checked_links": active_jobs[job_id].get("checked_links", 0),
            "total_links": active_jobs[job_id].get("total_links", 0)
        }))

# The scrapy spider functionality is now handled by ScrapyRunner class

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
