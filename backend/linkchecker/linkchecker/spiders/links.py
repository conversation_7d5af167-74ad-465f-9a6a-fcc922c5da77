import scrapy
from urllib.parse import urljoin, urlparse
import re


class <PERSON><PERSON><PERSON><PERSON><PERSON>(scrapy.Spider):
    name = "links"

    # Custom settings to disable meta refresh middleware and handle more status codes
    custom_settings = {
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware': None,
        },
        'HTTPERROR_ALLOWED_CODES': [403, 404, 500, 502, 503],  # Allow these error codes to be processed
    }

    def __init__(self, start_url=None, ignore_patterns=None, *args, **kwargs):
        super(LinksSpider, self).__init__(*args, **kwargs)
        if start_url:
            self.start_urls = [start_url]
            parsed_url = urlparse(start_url)
            self.allowed_domains = [parsed_url.netloc]
            self.base_domain = parsed_url.netloc
        else:
            self.start_urls = ["https://example.com"]
            self.allowed_domains = ["example.com"]
            self.base_domain = "example.com"

        self.found_links = set()
        self.internal_links = set()
        self.external_links = set()

        # Parse ignore patterns from JSON string if provided
        if ignore_patterns and isinstance(ignore_patterns, str):
            import json
            try:
                self.ignore_patterns = json.loads(ignore_patterns)
            except json.JSONDecodeError:
                self.ignore_patterns = []
        else:
            self.ignore_patterns = ignore_patterns or []
        # Add default ignore patterns for common problematic URLs
        default_ignore_patterns = [
            r'https://fonts\.googleapis\.com/?',  # Match with or without trailing slash
            r'https://www\.googletagmanager\.com/?',
            r'https://www\.google-analytics\.com/?',
            r'https://googleads\.g\.doubleclick\.net/?',
            r'https://connect\.facebook\.net/?',
            r'https://platform\.twitter\.com/?',
            r'https://www\.facebook\.com/tr/?',
            r'https://analytics\.google\.com/?',
            r'https://tagmanager\.google\.com/?',
            r'mailto:',
            r'tel:',
            r'javascript:',
            r'data:',
            r'#'  # Fragment-only links
        ]
        self.ignore_patterns.extend(default_ignore_patterns)



    def parse(self, response):
        self.logger.info(f"Parsing page: {response.url} (status: {response.status})")

        # Handle error responses
        if response.status >= 400:
            self.logger.warning(f"Received {response.status} status for {response.url}")
            # For error pages, we might still want to extract any links present
            # but we should be more careful about the content
            if response.status == 403:
                self.logger.info(f"Access forbidden to {response.url} - this may be due to bot protection")
            elif response.status == 404:
                self.logger.info(f"Page not found: {response.url}")
            # Continue processing to extract any links that might be present in error pages

        # Extract all links from various HTML elements

        # 1. Extract links from <a> elements
        link_elements = response.css('a')
        for link_element in link_elements:
            href = link_element.css('::attr(href)').get()
            if href:
                yield from self._process_url(response, href, 'anchor', link_element)

        # 2. Extract links from <link> elements (canonical, stylesheets, etc.)
        link_tags = response.css('link')
        for link_tag in link_tags:
            href = link_tag.css('::attr(href)').get()
            rel = link_tag.css('::attr(rel)').get() or 'link'
            if href:
                yield from self._process_url(response, href, f'link[rel="{rel}"]', link_tag)

        # 3. Extract URLs from <meta> elements (refresh redirects, og:url, etc.)
        meta_tags = response.css('meta')
        for meta_tag in meta_tags:
            # Meta refresh redirects
            http_equiv = meta_tag.css('::attr(http-equiv)').get()
            if http_equiv and http_equiv.lower() == 'refresh':
                content = meta_tag.css('::attr(content)').get()
                if content and 'url=' in content.lower():
                    url_part = content.split('url=', 1)[1].strip()
                    if url_part:
                        yield from self._process_url(response, url_part, 'meta[http-equiv="refresh"]', meta_tag, follow=False)

            # Open Graph and Twitter Card URLs
            property_attr = meta_tag.css('::attr(property)').get()
            name_attr = meta_tag.css('::attr(name)').get()
            content = meta_tag.css('::attr(content)').get()

            if content and (
                (property_attr and property_attr in ['og:url', 'og:image', 'og:video', 'og:audio']) or
                (name_attr and name_attr in ['twitter:url', 'twitter:image', 'twitter:player'])
            ):
                attr_name = property_attr or name_attr
                yield from self._process_url(response, content, f'meta[{attr_name}]', meta_tag)

        # 4. Extract URLs from <img> elements
        img_elements = response.css('img')
        for img_element in img_elements:
            src = img_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'img[src]', img_element)

        # 5. Extract URLs from <script> elements
        script_elements = response.css('script')
        for script_element in script_elements:
            src = script_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'script[src]', script_element)

        # 6. Extract URLs from <iframe> elements
        iframe_elements = response.css('iframe')
        for iframe_element in iframe_elements:
            src = iframe_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'iframe[src]', iframe_element)

        # 7. Extract URLs from <form> elements
        form_elements = response.css('form')
        for form_element in form_elements:
            action = form_element.css('::attr(action)').get()
            if action:
                yield from self._process_url(response, action, 'form[action]', form_element)

        # 8. Extract URLs from <area> elements (image maps)
        area_elements = response.css('area')
        for area_element in area_elements:
            href = area_element.css('::attr(href)').get()
            if href:
                yield from self._process_url(response, href, 'area[href]', area_element)

        # 9. Extract URLs from <object> and <embed> elements
        object_elements = response.css('object')
        for object_element in object_elements:
            # object data attribute
            data = object_element.css('::attr(data)').get()
            if data:
                yield from self._process_url(response, data, 'object[data]', object_element)

            # object classid attribute
            classid = object_element.css('::attr(classid)').get()
            if classid:
                yield from self._process_url(response, classid, 'object[classid]', object_element)

            # object codebase attribute
            codebase = object_element.css('::attr(codebase)').get()
            if codebase:
                yield from self._process_url(response, codebase, 'object[codebase]', object_element)

        embed_elements = response.css('embed')
        for embed_element in embed_elements:
            src = embed_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'embed[src]', embed_element)

        # 10. Extract URLs from <applet> elements
        applet_elements = response.css('applet')
        for applet_element in applet_elements:
            codebase = applet_element.css('::attr(codebase)').get()
            if codebase:
                yield from self._process_url(response, codebase, 'applet[codebase]', applet_element)

        # 11. Extract URLs from <audio> elements
        audio_elements = response.css('audio')
        for audio_element in audio_elements:
            src = audio_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'audio[src]', audio_element)

        # 12. Extract URLs from <video> elements
        video_elements = response.css('video')
        for video_element in video_elements:
            # video src attribute
            src = video_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'video[src]', video_element)

            # video poster attribute
            poster = video_element.css('::attr(poster)').get()
            if poster:
                yield from self._process_url(response, poster, 'video[poster]', video_element)

        # 13. Extract URLs from <source> elements
        source_elements = response.css('source')
        for source_element in source_elements:
            src = source_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'source[src]', source_element)

        # 14. Extract URLs from <input> elements
        input_elements = response.css('input')
        for input_element in input_elements:
            # input src attribute (for image inputs)
            src = input_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'input[src]', input_element)

            # input formaction attribute
            formaction = input_element.css('::attr(formaction)').get()
            if formaction:
                yield from self._process_url(response, formaction, 'input[formaction]', input_element)

        # 15. Extract URLs from <button> elements
        button_elements = response.css('button')
        for button_element in button_elements:
            formaction = button_element.css('::attr(formaction)').get()
            if formaction:
                yield from self._process_url(response, formaction, 'button[formaction]', button_element)

        # 16. Extract URLs from citation elements
        # <blockquote cite="...">
        blockquote_elements = response.css('blockquote')
        for blockquote_element in blockquote_elements:
            cite = blockquote_element.css('::attr(cite)').get()
            if cite:
                yield from self._process_url(response, cite, 'blockquote[cite]', blockquote_element)

        # <q cite="...">
        q_elements = response.css('q')
        for q_element in q_elements:
            cite = q_element.css('::attr(cite)').get()
            if cite:
                yield from self._process_url(response, cite, 'q[cite]', q_element)

        # <del cite="...">
        del_elements = response.css('del')
        for del_element in del_elements:
            cite = del_element.css('::attr(cite)').get()
            if cite:
                yield from self._process_url(response, cite, 'del[cite]', del_element)

        # <ins cite="...">
        ins_elements = response.css('ins')
        for ins_element in ins_elements:
            cite = ins_element.css('::attr(cite)').get()
            if cite:
                yield from self._process_url(response, cite, 'ins[cite]', ins_element)

        # 17. Extract URLs from document-level elements
        # <body background="...">
        body_elements = response.css('body')
        for body_element in body_elements:
            background = body_element.css('::attr(background)').get()
            if background:
                yield from self._process_url(response, background, 'body[background]', body_element)

        # <html manifest="...">
        html_elements = response.css('html')
        for html_element in html_elements:
            manifest = html_element.css('::attr(manifest)').get()
            if manifest:
                yield from self._process_url(response, manifest, 'html[manifest]', html_element)

        # <head profile="...">
        head_elements = response.css('head')
        for head_element in head_elements:
            profile = head_element.css('::attr(profile)').get()
            if profile:
                yield from self._process_url(response, profile, 'head[profile]', head_element)

        # 18. Extract URLs from additional attributes
        # <img longdesc="..."> (we already have src)
        for img_element in response.css('img'):
            longdesc = img_element.css('::attr(longdesc)').get()
            if longdesc:
                yield from self._process_url(response, longdesc, 'img[longdesc]', img_element)

        # <iframe longdesc="..."> (we already have src)
        for iframe_element in response.css('iframe'):
            longdesc = iframe_element.css('::attr(longdesc)').get()
            if longdesc:
                yield from self._process_url(response, longdesc, 'iframe[longdesc]', iframe_element)

        # <frame src="..." longdesc="...">
        frame_elements = response.css('frame')
        for frame_element in frame_elements:
            src = frame_element.css('::attr(src)').get()
            if src:
                yield from self._process_url(response, src, 'frame[src]', frame_element)

            longdesc = frame_element.css('::attr(longdesc)').get()
            if longdesc:
                yield from self._process_url(response, longdesc, 'frame[longdesc]', frame_element)

        # <command icon="...">
        command_elements = response.css('command')
        for command_element in command_elements:
            icon = command_element.css('::attr(icon)').get()
            if icon:
                yield from self._process_url(response, icon, 'command[icon]', command_element)

    def _process_url(self, response, url, element_type, element, follow=True):
        """Process a URL found in any HTML element"""
        # Convert relative URLs to absolute URLs
        absolute_url = urljoin(response.url, url)

        # Clean the URL (remove fragments)
        clean_url = absolute_url.split('#')[0]

        # Check if URL should be ignored
        if self._should_ignore_url(clean_url):
            return

        if clean_url and clean_url not in self.found_links:
            self.found_links.add(clean_url)

            # Extract appropriate text based on element type
            anchor_text = self._extract_element_text(element, element_type)

            parsed_link = urlparse(clean_url)

            # Categorize as internal or external
            if parsed_link.netloc == self.base_domain or parsed_link.netloc == '':
                self.internal_links.add(clean_url)

                # Follow internal links for recursive crawling (only for anchor links and if follow=True)
                if element_type == 'anchor' and follow and self.is_valid_url(clean_url):
                    yield response.follow(clean_url, self.parse)
            else:
                self.external_links.add(clean_url)

            # Yield the link for processing
            yield {
                'url': clean_url,
                'source_page': response.url,
                'link_type': 'internal' if parsed_link.netloc == self.base_domain or parsed_link.netloc == '' else 'external',
                'anchor_text': anchor_text,
                'element_type': element_type
            }

    def _should_ignore_url(self, url):
        """Check if URL should be ignored based on ignore patterns"""
        import re
        for pattern in self.ignore_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        return False

    def _extract_element_text(self, element, element_type):
        """Extract appropriate text description for different element types"""
        if element_type == 'anchor':
            # For anchor elements, extract text content
            anchor_text = element.css('::text').get()
            if not anchor_text:
                anchor_text = element.css('*::text').getall()
                anchor_text = ' '.join([text.strip() for text in anchor_text if text.strip()])

            # If still no text, check for common attributes
            if not anchor_text:
                anchor_text = (
                    element.css('::attr(title)').get() or
                    element.css('::attr(alt)').get() or
                    element.css('img::attr(alt)').get() or
                    'No text'
                )
        elif element_type.startswith('img'):
            # For images, use alt text or title
            anchor_text = (
                element.css('::attr(alt)').get() or
                element.css('::attr(title)').get() or
                'Image'
            )
        elif element_type.startswith('link'):
            # For link elements, use title or rel attribute
            anchor_text = (
                element.css('::attr(title)').get() or
                f"Link ({element.css('::attr(rel)').get() or 'unknown'})"
            )
        elif element_type.startswith('meta'):
            # For meta elements, describe the type
            property_attr = element.css('::attr(property)').get()
            name_attr = element.css('::attr(name)').get()
            if property_attr:
                anchor_text = f"Meta property: {property_attr}"
            elif name_attr:
                anchor_text = f"Meta name: {name_attr}"
            else:
                anchor_text = "Meta refresh"
        elif element_type.startswith('script'):
            anchor_text = "Script source"
        elif element_type.startswith('iframe'):
            anchor_text = (
                element.css('::attr(title)').get() or
                element.css('::attr(name)').get() or
                "Iframe source"
            )
        elif element_type.startswith('form'):
            anchor_text = (
                element.css('::attr(name)').get() or
                element.css('::attr(id)').get() or
                "Form action"
            )
        elif element_type.startswith('area'):
            anchor_text = (
                element.css('::attr(alt)').get() or
                element.css('::attr(title)').get() or
                "Image map area"
            )
        elif element_type.startswith('object'):
            anchor_text = (
                element.css('::attr(title)').get() or
                "Object data"
            )
        elif element_type.startswith('embed'):
            anchor_text = (
                element.css('::attr(title)').get() or
                "Embed source"
            )
        elif element_type.startswith('applet'):
            anchor_text = (
                element.css('::attr(name)').get() or
                element.css('::attr(alt)').get() or
                "Applet codebase"
            )
        elif element_type.startswith('audio'):
            anchor_text = (
                element.css('::attr(title)').get() or
                "Audio source"
            )
        elif element_type.startswith('video'):
            if 'poster' in element_type:
                anchor_text = "Video poster"
            else:
                anchor_text = (
                    element.css('::attr(title)').get() or
                    "Video source"
                )
        elif element_type.startswith('source'):
            anchor_text = "Media source"
        elif element_type.startswith('input'):
            if 'src' in element_type:
                anchor_text = (
                    element.css('::attr(alt)').get() or
                    element.css('::attr(title)').get() or
                    "Input image"
                )
            else:
                anchor_text = "Form action"
        elif element_type.startswith('button'):
            anchor_text = (
                element.css('::text').get() or
                element.css('::attr(title)').get() or
                "Button action"
            )
        elif element_type.startswith('blockquote'):
            anchor_text = "Blockquote citation"
        elif element_type.startswith('q['):
            anchor_text = "Quote citation"
        elif element_type.startswith('del'):
            anchor_text = "Deletion citation"
        elif element_type.startswith('ins'):
            anchor_text = "Insertion citation"
        elif element_type.startswith('body'):
            anchor_text = "Body background"
        elif element_type.startswith('html'):
            anchor_text = "HTML manifest"
        elif element_type.startswith('head'):
            anchor_text = "Head profile"
        elif element_type.startswith('frame'):
            if 'longdesc' in element_type:
                anchor_text = "Frame description"
            else:
                anchor_text = (
                    element.css('::attr(name)').get() or
                    element.css('::attr(title)').get() or
                    "Frame source"
                )
        elif element_type.startswith('command'):
            anchor_text = (
                element.css('::attr(label)').get() or
                "Command icon"
            )
        elif 'longdesc' in element_type:
            anchor_text = f"{element_type.split('[')[0].title()} description"
        else:
            anchor_text = f"Link from {element_type}"

        # Clean and limit anchor text
        anchor_text = anchor_text.strip() if anchor_text else '[No text]'
        if len(anchor_text) > 100:
            anchor_text = anchor_text[:97] + '...'

        return anchor_text

    def is_valid_url(self, url):
        """Check if URL should be crawled"""
        # Skip common file extensions that don't need crawling
        skip_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.svg', '.css', '.js', '.ico', '.zip', '.tar', '.gz']

        for ext in skip_extensions:
            if url.lower().endswith(ext):
                return False

        # Skip mailto and tel links
        if url.startswith(('mailto:', 'tel:', 'javascript:')):
            return False

        return True
