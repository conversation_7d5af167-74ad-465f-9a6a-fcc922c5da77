import asyncio
import subprocess
import json
import tempfile
import os
from typing import List, Dict, Any


class ScrapyRunner:
    """Runner for Scrapy spider to extract links"""
    
    def __init__(self):
        self.spider_path = "linkchecker/linkchecker/spiders/links.py"
    
    async def crawl_links(self, url: str, progress_callback=None, ignore_patterns=None) -> List[Dict[str, Any]]:
        """
        Crawl a website and extract all links

        Args:
            url: URL to crawl
            progress_callback: Optional callback for progress updates
            ignore_patterns: Optional list of regex patterns to ignore

        Returns:
            List of extracted link data with metadata
        """
        if progress_callback:
            await progress_callback(f"Starting to crawl {url}", 0, 6)
        
        # Create temporary file for output
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            output_file = temp_file.name
        
        try:
            # Run Scrapy spider - use the known working path
            scrapy_cmd = ['/Users/<USER>/Library/Python/3.9/bin/scrapy', 'crawl', 'links',
                         '-a', f'start_url={url}',
                         '-o', output_file,
                         '-s', 'LOG_LEVEL=WARNING']

            # Add ignore patterns if provided
            if ignore_patterns:
                import json
                patterns_json = json.dumps(ignore_patterns)
                scrapy_cmd.extend(['-a', f'ignore_patterns={patterns_json}'])
            
            if progress_callback:
                await progress_callback("Starting website crawling...", 1, 6)

            # Execute Scrapy in the linkchecker directory with progress monitoring
            process = await asyncio.create_subprocess_exec(
                *scrapy_cmd,
                cwd='linkchecker',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # Monitor crawling progress
            progress_task = None
            if progress_callback:
                progress_task = asyncio.create_task(
                    self._monitor_crawling_progress(progress_callback)
                )

            try:
                stdout, stderr = await process.communicate()
            finally:
                if progress_task:
                    progress_task.cancel()
                    try:
                        await progress_task
                    except asyncio.CancelledError:
                        pass



            # Check if the process failed
            if process.returncode != 0:
                error_msg = f"Scrapy process failed with return code {process.returncode}"
                if stderr:
                    error_msg += f": {stderr.decode('utf-8', errors='ignore')}"
                raise Exception(error_msg)

            if progress_callback:
                await progress_callback("Website crawling completed", 5, 6)
            
            # Read the output file and return link data with source pages
            links_data = []
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r') as f:
                        data = json.load(f)

                    # Extract unique URLs from the scraped data with source page info
                    seen_urls = set()
                    for item in data:
                        if 'url' in item and item['url'] not in seen_urls:
                            links_data.append({
                                'url': item['url'],
                                'source_page': item.get('source_page', url),
                                'link_type': item.get('link_type', 'unknown'),
                                'anchor_text': item.get('anchor_text', 'No text'),
                                'element_type': item.get('element_type', 'anchor')
                            })
                            seen_urls.add(item['url'])

                except json.JSONDecodeError:
                    # If JSON parsing fails, try reading line by line
                    with open(output_file, 'r') as f:
                        for line in f:
                            try:
                                item = json.loads(line.strip())
                                if 'url' in item and item['url'] not in seen_urls:
                                    links_data.append({
                                        'url': item['url'],
                                        'source_page': item.get('source_page', url),
                                        'link_type': item.get('link_type', 'unknown'),
                                        'anchor_text': item.get('anchor_text', 'No text')
                                    })
                                    seen_urls.add(item['url'])
                            except json.JSONDecodeError:
                                continue
            
            if progress_callback:
                await progress_callback(f"Found {len(links_data)} unique links", 6, 6)

            return links_data
            
        except Exception as e:
            if progress_callback:
                await progress_callback(f"Error during crawling: {str(e)}")

            # Re-raise the exception instead of returning fallback data
            raise e
        
        finally:
            # Clean up temporary file
            try:
                os.unlink(output_file)
            except:
                pass
    
    def is_available(self) -> bool:
        """
        Check if Scrapy is available
        
        Returns:
            True if Scrapy is available, False otherwise
        """
        try:
            subprocess.run(['scrapy', 'version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    async def _monitor_crawling_progress(self, progress_callback):
        """Monitor crawling progress and send updates"""
        try:
            messages = [
                "Analyzing website structure...",
                "Discovering internal links...",
                "Finding external references...",
                "Extracting all page links..."
            ]

            for i, message in enumerate(messages):
                await asyncio.sleep(1.5)  # Update every 1.5 seconds
                await progress_callback(message, i + 2, 6)  # Steps 2-5 out of 6

        except asyncio.CancelledError:
            pass


# Example usage
async def main():
    runner = ScrapyRunner()
    
    if not runner.is_available():
        print("Scrapy is not available")
        return
    
    links = await runner.crawl_links("https://example.com")
    print(f"Found {len(links)} links:")
    for link in links[:10]:  # Show first 10
        print(f"  {link}")


if __name__ == "__main__":
    asyncio.run(main())
