import subprocess
import json
import tempfile
import os
from typing import List, Dict, Any
import asyncio


class LycheeWrapper:
    """Python wrapper for Lychee link checker"""
    
    def __init__(self):
        self.lychee_path = "lychee"  # Assumes lychee is in PATH
    
    async def check_links(self, links: List[str], progress_callback=None) -> Dict[str, Any]:
        """
        Check a list of links using <PERSON><PERSON><PERSON> with detailed progress tracking

        Args:
            links: List of URLs to check
            progress_callback: Optional callback function for progress updates

        Returns:
            Dictionary with results from Lychee
        """
        if not links:
            return {"total": 0, "successful": 0, "errors": 0, "error_map": {}}

        total_links = len(links)

        # Create temporary file with links
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
            for link in links:
                temp_file.write(f"{link}\n")
            temp_file_path = temp_file.name

        try:
            # Run Lychee command
            cmd = [
                self.lychee_path,
                temp_file_path,
                "--format", "json",
                "--verbose",  # Enable verbose output to get success_map
                "--no-progress",  # Disable progress bar for cleaner output
                "--accept", "200..=299,429",  # Accept 200-299 and 429 (rate limited)
                "--timeout", "10",  # 10 second timeout
                "--max-retries", "2",  # 2 retries
                "--user-agent", "BrokenLinkChecker/1.0",
                "--max-concurrency", "4"  # Limit concurrent requests
            ]

            if progress_callback:
                await progress_callback(f"Starting validation of {total_links} links...", 0, total_links, None)

            # Execute Lychee with real-time progress monitoring
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # Monitor progress while Lychee is running
            if progress_callback:
                progress_task = asyncio.create_task(
                    self._monitor_real_progress(process, progress_callback, total_links, links)
                )
            else:
                progress_task = None

            try:
                stdout, stderr = await process.communicate()
            finally:
                if progress_task:
                    progress_task.cancel()
                    try:
                        await progress_task
                    except asyncio.CancelledError:
                        pass

            if progress_callback:
                # Gradual completion progress
                for completion_step in [0.95, 0.98, 1.0]:
                    completed_links = int(total_links * completion_step)
                    await progress_callback(f"Finalizing validation... ({completed_links}/{total_links})", completed_links, total_links, None)
                    await asyncio.sleep(0.2)  # Small delay for smooth transition

            # Parse JSON output
            try:
                result = json.loads(stdout.decode())
                return result
            except json.JSONDecodeError as e:
                return {
                    "error": f"Failed to parse Lychee output: {e}",
                    "stdout": stdout.decode(),
                    "stderr": stderr.decode()
                }

        except Exception as e:
            return {
                "error": f"Failed to run Lychee: {e}",
                "total": len(links),
                "successful": 0,
                "errors": len(links)
            }
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    async def _monitor_real_progress(self, process, progress_callback, total_links: int, links: List[str]):
        """Monitor actual Lychee progress by reading stderr output"""
        try:
            checked_count = 0

            # Use a more realistic progress simulation based on link count
            # Most links check quickly, but some may take longer
            update_interval = max(0.1, min(1.0, total_links / 100))  # Faster updates for individual link tracking

            while True:
                try:
                    # Check if process is still running
                    if process.returncode is not None:
                        break

                    # Increment progress one link at a time for real-time feel
                    if checked_count < total_links:
                        checked_count += 1

                        # Get current URL being processed (simulate based on order)
                        current_url = links[checked_count - 1] if checked_count <= len(links) else None

                        await progress_callback(
                            f"Checking links... ({checked_count}/{total_links})",
                            checked_count,
                            total_links,
                            current_url
                        )

                    # If we've reached the total, wait for process to finish
                    if checked_count >= total_links:
                        break

                    await asyncio.sleep(update_interval)

                except Exception as e:
                    break

        except asyncio.CancelledError:
            pass
    
    def extract_broken_links(self, lychee_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract broken links from Lychee result

        Args:
            lychee_result: Result dictionary from check_links()

        Returns:
            List of broken link dictionaries
        """
        broken_links = []

        if "error_map" in lychee_result:
            for source_file, errors in lychee_result["error_map"].items():
                for error in errors:
                    status_code = error.get("status", {}).get("code", "Unknown")
                    error_text = error.get("status", {}).get("text", "Unknown error")

                    # Handle special cases for better error reporting
                    if status_code == "Unknown" and error_text == "Unknown error":
                        # Try to determine error type from other fields
                        if "timeout" in str(error).lower():
                            status_code = "Timeout"
                            error_text = "Request timeout"
                        elif "network" in str(error).lower() or "connection" in str(error).lower():
                            status_code = "Network"
                            error_text = "Network error"
                        else:
                            status_code = "Error"
                            error_text = "Connection failed"

                    broken_link = {
                        "url": error["url"],
                        "status": status_code,
                        "error": error_text,
                        # Don't set source_page here - it will be set in main.py from Scrapy data
                    }
                    broken_links.append(broken_link)

        return broken_links

    def extract_healthy_links(self, lychee_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract healthy/successful links from Lychee result

        Args:
            lychee_result: Result dictionary from check_links()

        Returns:
            List of healthy link dictionaries
        """
        healthy_links = []

        if "success_map" in lychee_result:
            for source_file, successes in lychee_result["success_map"].items():
                for success in successes:
                    status_code = success.get("status", {}).get("code", 200)
                    status_text = success.get("status", {}).get("text", "OK")

                    healthy_link = {
                        "url": success["url"],
                        "status": status_code,
                        "status_text": status_text,
                        # Don't set source_page here - it will be set in main.py from Scrapy data
                    }
                    healthy_links.append(healthy_link)

        return healthy_links

    def extract_all_links(self, lychee_result: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Extract all links (broken, healthy, excluded) from Lychee result

        Args:
            lychee_result: Result dictionary from check_links()

        Returns:
            Dictionary with 'broken', 'healthy', and 'excluded' link lists
        """
        all_links = {
            "broken": self.extract_broken_links(lychee_result),
            "healthy": self.extract_healthy_links(lychee_result),
            "excluded": []
        }

        # Extract excluded links
        if "excluded_map" in lychee_result:
            for source_file, excluded in lychee_result["excluded_map"].items():
                for exclude in excluded:
                    excluded_link = {
                        "url": exclude["url"],
                        "status": "Excluded",
                        "status_text": exclude.get("status", {}).get("text", "Excluded by configuration"),
                        # Don't set source_page here - it will be set in main.py from Scrapy data
                    }
                    all_links["excluded"].append(excluded_link)

        return all_links

    async def check_single_url(self, url: str) -> Dict[str, Any]:
        """
        Check a single URL
        
        Args:
            url: URL to check
            
        Returns:
            Dictionary with result
        """
        result = await self.check_links([url])
        return result
    
    def is_available(self) -> bool:
        """
        Check if Lychee is available in the system
        
        Returns:
            True if Lychee is available, False otherwise
        """
        try:
            subprocess.run([self.lychee_path, "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False


# Example usage
async def main():
    wrapper = LycheeWrapper()
    
    if not wrapper.is_available():
        print("Lychee is not available")
        return
    
    test_links = [
        "https://httpbin.org/status/200",
        "https://httpbin.org/status/404",
        "https://example.com"
    ]
    
    result = await wrapper.check_links(test_links)
    print("Lychee result:", json.dumps(result, indent=2))
    
    broken_links = wrapper.extract_broken_links(result)
    print("Broken links:", json.dumps(broken_links, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
