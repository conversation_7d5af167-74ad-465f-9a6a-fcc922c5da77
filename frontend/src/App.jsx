import { useState, useEffect, useRef } from 'react'

// HTTP Status Code definitions
const getStatusDescription = (statusCode) => {
  const statusCodes = {
    // 2xx Success
    200: '200 OK',
    201: '201 Created',
    202: '202 Accepted',
    204: '204 No Content',

    // 3xx Redirection
    301: '301 Moved Permanently',
    302: '302 Found',
    304: '304 Not Modified',
    307: '307 Temporary Redirect',
    308: '308 Permanent Redirect',

    // 4xx Client Error
    400: '400 Bad Request',
    401: '401 Unauthorized',
    403: '403 Forbidden',
    404: '404 Not Found',
    405: '405 Method Not Allowed',
    408: '408 Request Timeout',
    409: '409 Conflict',
    410: '410 Gone',
    429: '429 Too Many Requests',

    // 5xx Server Error
    500: '500 Internal Server Error',
    501: '501 Not Implemented',
    502: '502 Bad Gateway',
    503: '503 Service Unavailable',
    504: '504 Gateway Timeout',
    505: '505 HTTP Version Not Supported',

    // Non-standard codes
    999: '999 Non-standard'
  }

  // Handle special non-numeric status codes
  if (typeof statusCode === 'string') {
    switch (statusCode.toLowerCase()) {
      case 'timeout':
        return 'Timeout'
      case 'network':
        return 'Network Error'
      case 'error':
        return 'Connection Error'
      default:
        return statusCode
    }
  }

  return statusCodes[statusCode] || (statusCode ? `${statusCode} Error` : 'Connection Failed')
}

function App() {
  const [url, setUrl] = useState('')
  const [isChecking, setIsChecking] = useState(false)
  const [progress, setProgress] = useState(0)
  const [brokenLinks, setBrokenLinks] = useState([])
  const [healthyLinks, setHealthyLinks] = useState([])
  const [allLinks, setAllLinks] = useState({ broken: [], healthy: [], excluded: [] })
  const [activeTab, setActiveTab] = useState('broken') // 'broken', 'healthy', 'all'
  const [statusMessage, setStatusMessage] = useState('')
  const [currentJobId, setCurrentJobId] = useState(null)
  const [error, setError] = useState('')
  const [totalLinks, setTotalLinks] = useState(0)
  const [checkedLinks, setCheckedLinks] = useState(0)
  const [wsConnected, setWsConnected] = useState(false)
  const [validationError, setValidationError] = useState('')
  const [isAborted, setIsAborted] = useState(false)

  const wsRef = useRef(null)

  // Helper function to format numbers with thousand separators
  const formatNumber = (num) => {
    return num.toLocaleString()
  }



  // URL validation function
  const validateUrl = (inputUrl) => {
    const trimmedUrl = inputUrl.trim()

    // Check if empty
    if (!trimmedUrl) {
      return 'URL is required'
    }

    // Remove protocol if present for validation
    const urlWithoutProtocol = trimmedUrl.replace(/^https?:\/\//, '').replace(/^\/\//, '')

    // Get the domain part (before any path)
    const domainPart = urlWithoutProtocol.split('/')[0]

    // Check if it contains at least one dot (domain format) or is localhost
    if (!domainPart.includes('.') && !domainPart.startsWith('localhost')) {
      return 'Please enter a valid domain (e.g., example.com)'
    }

    // Check if domain ends with just a dot (e.g., "string.")
    if (domainPart.endsWith('.')) {
      return 'Please enter a valid domain (e.g., example.com)'
    }

    // Check if there's text after the last dot (valid TLD)
    const parts = domainPart.split('.')
    const lastPart = parts[parts.length - 1]
    if (!lastPart || lastPart.length === 0) {
      return 'Please enter a valid domain (e.g., example.com)'
    }

    // Check for valid TLD (at least 2 characters) unless it's localhost
    if (lastPart.length < 2 && !domainPart.startsWith('localhost')) {
      return 'Please enter a valid domain (e.g., example.com)'
    }

    return '' // No error
  }

  // WebSocket connection
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        wsRef.current = new WebSocket('ws://localhost:8000/ws')
        
        wsRef.current.onopen = () => {
          console.log('WebSocket connected')
          setWsConnected(true)
          setError('')
        }
        
        wsRef.current.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)

            if (data.job_id === currentJobId) {
              if (data.progress !== undefined) {
                setProgress(data.progress)
              }

              if (data.message) {
                setStatusMessage(data.message)
              }

              if (data.total_links !== undefined) {
                setTotalLinks(data.total_links)
              }

              if (data.checked_links !== undefined) {
                setCheckedLinks(data.checked_links)
              }

              if (data.broken_links) {
                setBrokenLinks(data.broken_links)
              }

              if (data.healthy_links) {
                setHealthyLinks(data.healthy_links)
              }

              if (data.all_links) {
                setAllLinks(data.all_links)
              }

              if (data.status === 'completed' || data.status === 'error' || data.status === 'aborted') {
                setIsChecking(false)
                if (data.status === 'aborted') {
                  setIsAborted(true)
                }
              }
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
          }
        }
        
        wsRef.current.onclose = (event) => {
          console.log('WebSocket disconnected')
          setWsConnected(false)
          
          if (event.code !== 1000 && isChecking) {
            setTimeout(connectWebSocket, 3000)
          }
        }
        
        wsRef.current.onerror = (error) => {
          console.error('WebSocket connection error:', error)
          setWsConnected(false)
        }
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
        setWsConnected(false)
      }
    }
    
    connectWebSocket()
    
    return () => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close(1000, 'Component unmounting')
      }
    }
  }, [currentJobId, isChecking])

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate URL
    const validationErr = validateUrl(url)
    if (validationErr) {
      setValidationError(validationErr)
      return
    }

    setValidationError('')
    setIsChecking(true)
    setProgress(0)
    setBrokenLinks([])
    setHealthyLinks([])
    setAllLinks({ broken: [], healthy: [], excluded: [] })
    setActiveTab('broken')
    setIsAborted(false)
    setStatusMessage('Starting link check...')
    setError('')
    setTotalLinks(0)
    setCheckedLinks(0)
    
    try {

      const response = await fetch('http://localhost:8000/check-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: url.trim()
        }),
      })
      
      const result = await response.json()
      
      if (result.job_id) {
        setCurrentJobId(result.job_id)
        setStatusMessage(result.message)
      } else {
        const errorMsg = result.error || 'Failed to start link check'
        setError(errorMsg)
        setStatusMessage(errorMsg)
        setIsChecking(false)
      }
    } catch (error) {
      console.error('Error starting link check:', error)
      const errorMsg = 'Failed to connect to server. Please check if the backend is running.'
      setError(errorMsg)
      setStatusMessage(errorMsg)
      setIsChecking(false)
    }
  }

  const abortCheck = async () => {
    if (!currentJobId || !isChecking) return

    try {
      const response = await fetch(`http://localhost:8000/abort/${currentJobId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (result.status === 'aborted') {
        setStatusMessage('Scan aborted by user')
        setIsChecking(false)
        setIsAborted(true)
      } else {
        console.error('Failed to abort job:', result.error)
      }
    } catch (error) {
      console.error('Error aborting job:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <button
              onClick={() => window.location.reload()}
              className="flex items-start space-x-4 transition-colors group hover:cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlSpace="preserve"
                width="512"
                height="512"
                viewBox="0 0 512 512"
                className="h-8 w-8"
              >
                <path
                  fill="#155dfc"
                  fillRule="evenodd"
                  d="M126.159.003h259.682c67.068 0 121.947 54.879 121.947 121.946V390.05c0 67.067-54.879 121.946-121.947 121.946H126.159c-67.068 0-121.947-54.879-121.947-121.946V121.949C4.213 54.882 59.091.003 126.159.003"
                  clipRule="evenodd"
                  data-original="#4cd3aa"
                ></path>
                <path
                  fill="#fff"
                  d="M257.091 309.646a17.56 17.56 0 0 1 12.444-5.159c4.479 0 8.986 1.729 12.416 5.159s5.159 7.937 5.159 12.416c0 4.507-1.729 9.014-5.159 12.444l-50.06 50.06a72.7 72.7 0 0 1-23.839 15.874 73.5 73.5 0 0 1-28.177 5.613v-.028a73.9 73.9 0 0 1-28.205-5.584 72.6 72.6 0 0 1-23.811-15.874 73.4 73.4 0 0 1-15.902-23.811c-3.713-9.014-5.556-18.595-5.556-28.205h-.028a74.25 74.25 0 0 1 5.584-28.205 73.4 73.4 0 0 1 15.902-23.811l50.06-50.06a73.4 73.4 0 0 1 23.811-15.902 74.25 74.25 0 0 1 28.205-5.584v.028c9.609 0 19.19 1.842 28.205 5.556a73.4 73.4 0 0 1 23.811 15.902c3.43 3.43 5.131 7.909 5.131 12.416s-1.701 9.014-5.131 12.444-7.937 5.131-12.416 5.131c-4.507 0-9.014-1.701-12.444-5.131a37.8 37.8 0 0 0-12.387-8.305c-4.677-1.928-9.723-2.892-14.768-2.892-9.865 0-19.701 3.742-27.156 11.197l-50.06 50.06a37.8 37.8 0 0 0-8.306 12.387c-1.927 4.677-2.891 9.723-2.891 14.769s.964 10.091 2.891 14.769a37.8 37.8 0 0 0 8.306 12.387 37.8 37.8 0 0 0 12.387 8.306 38.9 38.9 0 0 0 14.768 2.892c9.836 0 19.701-3.742 27.156-11.197zm-2.182-107.292c-3.43 3.43-7.937 5.131-12.444 5.131-4.479 0-8.986-1.701-12.416-5.131s-5.159-7.937-5.159-12.444c0-4.479 1.729-8.986 5.159-12.416l50.06-50.06a73.4 73.4 0 0 1 23.811-15.902c9.042-3.713 18.624-5.584 28.205-5.584s19.19 1.871 28.205 5.584c8.702 3.6 16.81 8.9 23.811 15.902a73.1 73.1 0 0 1 15.902 23.811 74.1 74.1 0 0 1 5.584 28.205 74.1 74.1 0 0 1-5.584 28.176 73.5 73.5 0 0 1-15.902 23.839l-50.06 50.06a72.9 72.9 0 0 1-23.811 15.874 73.7 73.7 0 0 1-28.205 5.584 74 74 0 0 1-28.205-5.584 72.9 72.9 0 0 1-23.811-15.874c-3.43-3.43-5.131-7.937-5.131-12.444s1.701-8.986 5.131-12.416 7.937-5.159 12.416-5.159c4.507 0 9.014 1.729 12.444 5.159a37.3 37.3 0 0 0 12.387 8.277c4.705 1.956 9.751 2.92 14.768 2.92 9.865 0 19.701-3.742 27.156-11.197l50.06-50.06a38.4 38.4 0 0 0 8.306-12.387 39 39 0 0 0 2.891-14.769h.029c0-5.046-.992-10.063-2.92-14.769a38.2 38.2 0 0 0-8.306-12.416c-3.657-3.657-7.88-6.435-12.387-8.277a39.1 39.1 0 0 0-14.768-2.92v.029c-9.865 0-19.701 3.713-27.156 11.168z"
                  data-original="#ffffff"
                ></path>
              </svg>
              <div className="text-left">
                <h1 className="text-2xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                  Broken Link Checker
                </h1>
                {/* <p className="text-sm text-gray-600 mt-0.5">
                  Scan your website for broken links with real-time progress tracking
                </p> */}
              </div>
            </button>


          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* URL Input Form */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="px-5 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Free Website Check</h2>
              <p className="text-sm text-gray-600 mt-1">Enter a website URL to scan for broken links</p>
            </div>
            <div className="px-5 py-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
                    Website URL
                  </label>
                  <div className="flex gap-3">
                    <input
                      id="url"
                      type="text"
                      value={url}
                      onChange={(e) => {
                        setUrl(e.target.value)
                        if (validationError) setValidationError('')
                      }}
                      placeholder="example.com or https://example.com"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                      required
                      disabled={isChecking}
                    />
                    <button
                      type="submit"
                      disabled={isChecking || !url.trim()}
                      className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 hover:cursor-pointer focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                      {isChecking ? 'Scanning...' : 'Start Scan'}
                    </button>
                  </div>
                  {validationError && (
                    <p className="mt-2 text-sm text-red-600">
                      {validationError}
                    </p>
                  )}
                </div>
              </form>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg">
              <div className="p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <div className="mt-1 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Progress Section */}
          {(isChecking || totalLinks > 0) && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-3">
                      {isChecking ? (
                        <>
                          <svg className="animate-spin h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <h3 className="text-xl font-semibold text-gray-900">Scanning in Progress</h3>
                        </>
                      ) : isAborted ? (
                        <>
                          <svg className="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <h3 className="text-xl font-semibold text-gray-900">Scan Aborted</h3>
                        </>
                      ) : (
                        <>
                          <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <h3 className="text-xl font-semibold text-gray-900">Scan Complete</h3>
                        </>
                      )}
                    </div>
                    {isChecking && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {progress}%
                      </span>
                    )}
                  </div>
                  
                  {/* Connection Status */}
                  <div className="flex items-center space-x-2">
                    {isChecking ? (
                      wsConnected ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-sm text-green-600 font-medium">Live</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-sm text-red-600 font-medium">Offline</span>
                        </div>
                      )
                    ) : isAborted ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span className="text-sm text-orange-600 font-medium">Aborted</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-green-600 font-medium">Complete</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {/* Progress Bar */}
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      {isChecking ? (statusMessage || 'Processing...') : isAborted ? 'Scan aborted by user' : 'Scan completed successfully'}
                    </span>
                    <span className="font-medium text-gray-900">{isChecking ? progress : isAborted ? progress : 100}%</span>
                  </div>
                  
                  {/* GitHub-style Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div className="relative h-full">
                      {/* Background segments */}
                      <div className="absolute inset-0 flex">
                        {Array.from({ length: 20 }, (_, i) => {
                          const currentProgress = isChecking ? progress : isAborted ? progress : 100;
                          return (
                            <div
                              key={i}
                              className={`flex-1 h-full ${
                                i < Math.floor((currentProgress / 100) * 20)
                                  ? 'bg-green-500'
                                  : i === Math.floor((currentProgress / 100) * 20) && currentProgress % 5 !== 0
                                  ? 'bg-green-300'
                                  : 'bg-gray-200'
                              } ${i > 0 ? 'border-l border-white' : ''}`}
                              style={{
                                width: '5%',
                                opacity: i < Math.floor((currentProgress / 100) * 20) ? 1 : 0.3
                              }}
                            />
                          );
                        })}
                      </div>
                      
                      {/* Animated progress fill */}
                      <div
                        className="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full transition-all duration-700 ease-out"
                        style={{ width: `${isChecking ? progress : 100}%` }}
                      />
                    </div>
                  </div>

                  {/* Abort button */}
                  {isChecking && (
                    <div className="flex justify-end mt-3">
                      <button
                        onClick={abortCheck}
                        className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        Abort scan
                      </button>
                    </div>
                  )}
                </div>

                {/* Statistics - Visual cards with smooth transitions */}
                <div className={`mt-4 pt-4 border-t border-gray-100 transition-all duration-300 ${
                  totalLinks > 0 ? 'opacity-100 max-h-20' : 'opacity-0 max-h-0 overflow-hidden'
                }`}>
                  <div className="flex items-center justify-start space-x-4 text-center">
                    {/* Total Links - Neutral */}
                    <div className="bg-gray-50 rounded-lg px-3 py-2 min-w-0 transition-all duration-200">
                      <div className="text-2xl font-bold text-gray-700">{formatNumber(totalLinks)}</div>
                      <div className="text-xs text-gray-700 uppercase tracking-wide">Links</div>
                    </div>

                    {/* Processed Links - Blue background */}
                    <div className="bg-blue-50 rounded-lg px-3 py-2 min-w-0 transition-all duration-200">
                      <div className="text-2xl font-bold text-blue-700">{formatNumber(brokenLinks.length + healthyLinks.length)}</div>
                      <div className="text-xs text-blue-700 uppercase tracking-wide">Processed</div>
                    </div>

                    {/* Broken Links - Red background */}
                    <div className="bg-red-50 rounded-lg px-3 py-2 min-w-0 transition-all duration-200">
                      <div className="text-2xl font-bold text-red-700">{formatNumber(brokenLinks.length)}</div>
                      <div className="text-xs text-red-700 uppercase tracking-wide">Broken</div>
                    </div>

                    {/* Healthy Links - Green background */}
                    <div className="bg-green-50 rounded-lg px-3 py-2 min-w-0 transition-all duration-200">
                      <div className="text-2xl font-bold text-green-700">
                        {(() => {
                          if (totalLinks === 0) return 0;
                          const checkedCount = brokenLinks.length + healthyLinks.length;
                          if (checkedCount === 0) return totalLinks > 0 ? 100 : 0;
                          return Math.round((healthyLinks.length / checkedCount) * 100);
                        })()}%
                      </div>
                      <div className="text-xs text-green-700 uppercase tracking-wide">Healthy</div>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          )}

          {/* Results Section */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Scan Results
                  </h2>
                </div>
              </div>

              {/* Tabs - Only show when scan is completed */}
              {totalLinks > 0 && !isChecking && (
                <div className="mt-4">
                  <nav className="flex space-x-1.5" aria-label="Tabs">
                    <button
                      onClick={() => setActiveTab('broken')}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors cursor-pointer ${
                        activeTab === 'broken'
                          ? 'bg-white text-gray-900 border border-gray-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-gray-900 border border-transparent hover:border-gray-300'
                      }`}
                    >
                      Broken Links ({formatNumber(brokenLinks.length)})
                    </button>
                    <button
                      onClick={() => setActiveTab('healthy')}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors cursor-pointer ${
                        activeTab === 'healthy'
                          ? 'bg-white text-gray-900 border border-gray-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-gray-900 border border-transparent hover:border-gray-300'
                      }`}
                    >
                      Healthy Links ({formatNumber(healthyLinks.length)})
                    </button>
                    <button
                      onClick={() => setActiveTab('all')}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors cursor-pointer ${
                        activeTab === 'all'
                          ? 'bg-white text-gray-900 border border-gray-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-white hover:text-gray-900 border border-transparent hover:border-gray-300'
                      }`}
                    >
                      All Links ({formatNumber(brokenLinks.length + healthyLinks.length)})
                    </button>
                  </nav>
                </div>
              )}
            </div>
            
            <div className="overflow-hidden">
              {/* Tab Content */}
              {totalLinks === 0 ? (
                <div className="px-6 py-12 text-center">
                  {isChecking ? (
                    <div className="space-y-3">
                      <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Scanning for links</h3>
                        <p className="text-sm text-gray-500 mt-1">This may take a few moments...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Ready to scan</h3>
                        <p className="text-sm text-gray-500 mt-1">Enter a website URL above to start checking for broken links.</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  {/* Broken Links Tab */}
                  {activeTab === 'broken' && (
                    <>
                      {brokenLinks.length === 0 ? (
                        <div className="px-6 py-12 text-center">
                          <div className="space-y-3">
                            <div className="w-12 h-12 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-900">No broken links found!</h3>
                              <p className="text-sm text-gray-500 mt-1">All {formatNumber(totalLinks)} links are working correctly.</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                <div className="overflow-x-auto rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200 table-fixed-layout">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider first:rounded-tl-lg col-status">
                          Status
                        </th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-url">
                          URL
                        </th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-anchor">
                          Anchor Text
                        </th>
                        <th className="px-3 pr-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider last:rounded-tr-lg col-source">
                          Linked from
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {brokenLinks.map((link, index) => (
                        <tr key={index} className={`hover:bg-gray-50 transition-colors ${index === brokenLinks.length - 1 ? 'last:rounded-b-lg' : ''}`}>
                          <td className={`px-6 py-4 whitespace-nowrap ${index === brokenLinks.length - 1 ? 'rounded-bl-lg' : ''}`}>
                            <span className="inline-flex items-center px-2 py-1 rounded text-xs font-normal bg-red-50 text-red-700 border border-red-200">
                              {link.status ? getStatusDescription(link.status) : 'Error'}
                            </span>
                          </td>
                          <td className="px-3 py-4">
                            <div className="text-sm">
                              <a
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 font-medium break-all"
                              >
                                {link.url}
                              </a>
                            </div>
                          </td>
                          <td className="px-3 py-4">
                            <div className="text-sm text-gray-700">
                              {link.anchor_text && link.anchor_text !== 'No text' ? (
                                <div className="break-words">
                                  <span>{link.anchor_text}</span>
                                  {link.element_type && link.element_type !== 'anchor' && (
                                    <div className="text-xs text-gray-500 mt-1">
                                      <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                        {link.element_type}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <span className="text-gray-400 italic">No text</span>
                              )}
                            </div>
                          </td>
                          <td className={`px-3 pr-6 py-4 ${index === brokenLinks.length - 1 ? 'rounded-br-lg' : ''}`}>
                            <div className="text-sm text-gray-500">
                              {link.source_page ? (
                                <a
                                  href={link.source_page}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 break-all"
                                >
                                  {link.source_page}
                                </a>
                              ) : (
                                <span className="text-gray-400 italic">Direct link</span>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                      )}
                    </>
                  )}

                  {/* Healthy Links Tab */}
                  {activeTab === 'healthy' && (
                    <>
                      {healthyLinks.length === 0 ? (
                        <div className="px-6 py-12 text-center">
                          <div className="space-y-3">
                            <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-900">No healthy links to display</h3>
                              <p className="text-sm text-gray-500 mt-1">Healthy links will appear here after scanning.</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="overflow-x-auto rounded-lg">
                          <table className="min-w-full divide-y divide-gray-200 table-fixed-layout">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider first:rounded-tl-lg col-status">
                                  Status
                                </th>
                                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-url">
                                  URL
                                </th>
                                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-anchor">
                                  Anchor Text
                                </th>
                                <th className="px-3 pr-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider last:rounded-tr-lg col-source">
                                  Linked from
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {healthyLinks.map((link, index) => (
                                <tr key={index} className={`hover:bg-gray-50 transition-colors ${index === healthyLinks.length - 1 ? 'last:rounded-b-lg' : ''}`}>
                                  <td className={`px-6 py-4 whitespace-nowrap ${index === healthyLinks.length - 1 ? 'rounded-bl-lg' : ''}`}>
                                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-normal bg-green-50 text-green-700 border border-green-200">
                                      {link.status ? getStatusDescription(link.status) : 'OK'}
                                    </span>
                                  </td>
                                  <td className="px-3 py-4">
                                    <div className="text-sm">
                                      <a
                                        href={link.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 font-medium break-all"
                                      >
                                        {link.url}
                                      </a>
                                    </div>
                                  </td>
                                  <td className="px-3 py-4">
                                    <div className="text-sm text-gray-700">
                                      {link.anchor_text && link.anchor_text !== 'No text' ? (
                                        <div className="break-words">
                                          <span>{link.anchor_text}</span>
                                          {link.element_type && link.element_type !== 'anchor' && (
                                            <div className="text-xs text-gray-500 mt-1">
                                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                                {link.element_type}
                                              </span>
                                            </div>
                                          )}
                                        </div>
                                      ) : (
                                        <span className="text-gray-400 italic">No text</span>
                                      )}
                                    </div>
                                  </td>
                                  <td className={`px-3 pr-6 py-4 ${index === healthyLinks.length - 1 ? 'rounded-br-lg' : ''}`}>
                                    <div className="text-sm text-gray-500">
                                      {link.source_page ? (
                                        <a
                                          href={link.source_page}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 break-all"
                                        >
                                          {link.source_page}
                                        </a>
                                      ) : (
                                        <span className="text-gray-400 italic">Direct link</span>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </>
                  )}

                  {/* All Links Tab */}
                  {activeTab === 'all' && (
                    <>
                      {totalLinks === 0 ? (
                        <div className="px-6 py-12 text-center">
                          <div className="space-y-3">
                            <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-900">No links to display</h3>
                              <p className="text-sm text-gray-500 mt-1">All links will appear here after scanning.</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="overflow-x-auto rounded-lg">
                          <table className="min-w-full divide-y divide-gray-200 table-fixed-layout">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider first:rounded-tl-lg col-status">
                                  Status
                                </th>
                                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-url">
                                  URL
                                </th>
                                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider col-anchor">
                                  Anchor Text
                                </th>
                                <th className="px-3 pr-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider last:rounded-tr-lg col-source">
                                  Linked from
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {[...brokenLinks, ...healthyLinks, ...allLinks.excluded].map((link, index) => {
                                const allLinksArray = [...brokenLinks, ...healthyLinks, ...allLinks.excluded];
                                const isLastRow = index === allLinksArray.length - 1;
                                const isBroken = brokenLinks.includes(link);
                                const isHealthy = healthyLinks.includes(link);
                                const isExcluded = allLinks.excluded.includes(link);

                                return (
                                  <tr key={index} className={`hover:bg-gray-50 transition-colors ${isLastRow ? 'last:rounded-b-lg' : ''}`}>
                                    <td className={`px-6 py-4 whitespace-nowrap ${isLastRow ? 'rounded-bl-lg' : ''}`}>
                                      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-normal border ${
                                        isBroken ? 'bg-red-50 text-red-700 border-red-200' :
                                        isHealthy ? 'bg-green-50 text-green-700 border-green-200' :
                                        'bg-gray-50 text-gray-700 border-gray-200'
                                      }`}>
                                        {isBroken ? (link.status ? getStatusDescription(link.status) : 'Error') :
                                         isHealthy ? (link.status ? getStatusDescription(link.status) : 'OK') :
                                         'Excluded'}
                                      </span>
                                    </td>
                                    <td className="px-3 py-4">
                                      <div className="text-sm">
                                        <a
                                          href={link.url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 font-medium break-all"
                                        >
                                          {link.url}
                                        </a>
                                      </div>
                                    </td>
                                    <td className="px-3 py-4">
                                      <div className="text-sm text-gray-700">
                                        {link.anchor_text && link.anchor_text !== 'No text' ? (
                                          <div className="break-words">
                                            <span>{link.anchor_text}</span>
                                            {link.element_type && link.element_type !== 'anchor' && (
                                              <div className="text-xs text-gray-500 mt-1">
                                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                                  {link.element_type}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        ) : (
                                          <span className="text-gray-400 italic">No text</span>
                                        )}
                                      </div>
                                    </td>
                                    <td className={`px-3 pr-6 py-4 ${isLastRow ? 'rounded-br-lg' : ''}`}>
                                      <div className="text-sm text-gray-500">
                                        {link.source_page ? (
                                          <a
                                            href={link.source_page}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:text-blue-800 break-all"
                                          >
                                            {link.source_page}
                                          </a>
                                        ) : (
                                          <span className="text-gray-400 italic">Direct link</span>
                                        )}
                                      </div>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>


    </div>
  )
}

export default App
