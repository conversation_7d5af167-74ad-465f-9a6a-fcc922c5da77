#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced progress tracking
"""

import requests
import json
import time
import websocket
import threading

def on_message(ws, message):
    """Handle WebSocket messages"""
    try:
        data = json.loads(message)
        if 'progress' in data:
            print(f"📊 Progress: {data['progress']}% - {data.get('message', '')}")
            if 'checked_links' in data and 'total_links' in data:
                print(f"   📈 Links: {data['checked_links']}/{data['total_links']} processed")
    except:
        pass

def on_error(ws, error):
    print(f"❌ WebSocket error: {error}")

def on_close(ws, close_status_code, close_msg):
    print("🔌 WebSocket connection closed")

def test_enhanced_progress():
    """Test the enhanced progress tracking"""
    print("🚀 Testing Enhanced Progress Tracking")
    print("=" * 50)
    
    # Start link checking
    print("1. Starting link check...")
    response = requests.post(
        "http://localhost:8000/check-links",
        json={"url": "https://httpbin.org"},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to start link check: {response.status_code}")
        return
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ Job started: {job_id}")
    
    # Connect to WebSocket for real-time updates
    print("\n2. Connecting to WebSocket for live updates...")
    ws = websocket.WebSocketApp(
        "ws://localhost:8000/ws",
        on_message=on_message,
        on_error=on_error,
        on_close=on_close
    )
    
    # Start WebSocket in a separate thread
    ws_thread = threading.Thread(target=ws.run_forever)
    ws_thread.daemon = True
    ws_thread.start()
    
    # Monitor progress for 20 seconds
    print("\n3. Monitoring progress (20 seconds)...")
    for i in range(20):
        time.sleep(1)
        
        # Get job status
        status_response = requests.get(f"http://localhost:8000/job/{job_id}")
        if status_response.status_code == 200:
            status = status_response.json()
            if status.get('status') in ['completed', 'error']:
                print(f"\n✅ Job {status.get('status')}: {status.get('message')}")
                break
    
    ws.close()
    print("\n🎉 Progress tracking test completed!")

if __name__ == "__main__":
    test_enhanced_progress()
